# Pytest Logging Issue Fix

## Problem Summary

The user encountered a `FileNotFoundError` when running pytest tests:

```
FileNotFoundError: [Errno 2] No such file or directory: 
'C:\\Users\\<USER>\\OneDrive - cogniron.com 1\\Desktop\\GRETAH-CaseForge\\GRETAH-CaseForge\\GretahAI_ScriptWeaver\\logs\\test_logs\\generated_tests\\test_TC_006_1_1749101355_merged.py_20250605_105927.log'
```

Additionally, there was a warning about unknown `pytest.mark.order` marker.

## Root Causes

1. **Missing pytest-order dependency**: The `GretahAI_ScriptWeaver/requirements.txt` was missing the `pytest-order>=1.0.0` dependency, even though the conftest.py and pytest.ini were configured to use it.

2. **Logging directory creation issue**: The `add_test_log_handler` function in `conftest.py` was using the full test node name (including directory path) to create log filenames, but wasn't ensuring the directory structure existed.

3. **Path handling bug**: When pytest runs `generated_tests/test_TC_006_1_1749101355_merged.py::test_step1_navigate`, the `test_node.name` includes the full path, which was being used directly in the log filename.

## Fixes Applied

### 1. Added Missing Dependency

**File**: `GretahAI_ScriptWeaver/requirements.txt`

Added the missing pytest-order dependency:
```
pytest-order>=1.0.0
```

### 2. Fixed Logging Path Handling

**File**: `GretahAI_ScriptWeaver/conftest.py`

Enhanced the `add_test_log_handler` function with:

- **Path extraction**: Extract just the filename from full paths using `Path(raw_name).stem`
- **Directory creation**: Ensure log directories exist before creating file handlers
- **Error handling**: Added try-catch blocks with fallback mechanisms
- **Robust fallbacks**: If directory creation fails, fall back to console logging

Key changes:
```python
# Extract just the filename without directory path to avoid path issues
raw_name = getattr(test_node, 'name', 'unknown_test')
if '/' in raw_name or '\\' in raw_name:
    # Extract just the filename part (remove directory path)
    test_name = Path(raw_name).stem  # Gets filename without extension
else:
    test_name = raw_name

# Ensure the log directory exists before creating the file handler
try:
    log_file_path.parent.mkdir(parents=True, exist_ok=True)
except Exception as e:
    logger.error(f"Failed to create log directory {log_file_path.parent}: {e}")
    # Fallback to a simpler path if directory creation fails
    log_file_path = Path(os.getcwd()) / f"test_log_{timestamp}.log"
```

## How to Apply the Fix

### Step 1: Install Missing Dependency

```bash
cd GretahAI_ScriptWeaver
pip install pytest-order>=1.0.0
```

Or reinstall all dependencies:
```bash
pip install -r requirements.txt --force-reinstall
```

### Step 2: Verify the Fix

Run the verification test:
```bash
cd GretahAI_ScriptWeaver
python -m pytest test_fix_verification.py -v
```

### Step 3: Test with Existing Files

Try running one of the existing test files:
```bash
python -m pytest generated_tests/test_TC_001_1_1749010620_merged.py -v
```

## Expected Results

After applying the fixes:

1. ✅ **No more FileNotFoundError**: Log directories will be created automatically
2. ✅ **No more pytest.mark.order warnings**: The pytest-order plugin will be properly recognized
3. ✅ **Proper log file creation**: Test-specific log files will be created in the correct location
4. ✅ **Fallback mechanisms**: If any logging issues occur, the system will gracefully fall back to console logging

## Additional Notes

- The fix maintains backward compatibility with existing test files
- Log files will now be created with just the test filename, not the full path
- The system is more robust and handles edge cases gracefully
- All existing pytest configuration in `pytest.ini` remains valid

## Verification Commands

```bash
# Check if pytest-order is installed
pip show pytest-order

# Run a simple test to verify logging
python -m pytest test_fix_verification.py::test_step1_navigate -v -s

# Check if log directories are created
ls -la logs/test_logs/

# Run existing tests
python -m pytest generated_tests/ -k "test_step1_navigate" --maxfail=1 -v
```

The fixes ensure that the pytest testing framework works reliably across different environments and handles various edge cases that could cause logging failures.
