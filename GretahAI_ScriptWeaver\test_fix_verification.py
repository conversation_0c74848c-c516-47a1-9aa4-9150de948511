#!/usr/bin/env python3
"""
Test script to verify the conftest.py logging fixes.
This script tests the logging directory creation and pytest-order plugin functionality.
"""

import pytest
from selenium.webdriver.support.ui import WebDriverWait
from selenium.webdriver.support import expected_conditions as EC
import time

@pytest.mark.order(1)
def test_step1_navigate(browser):
    """Test navigation to verify logging and pytest-order work correctly."""
    test_data = {
        "website_url": "https://httpbin.org/get"
    }
    try:
        # Navigate to a simple test URL
        test_url = test_data["website_url"]
        browser.get(test_url)
        
        # Wait for page to load
        WebDriverWait(browser, 10).until(
            lambda driver: driver.execute_script("return document.readyState") == "complete"
        )
        
        # Verify we're on the correct page
        assert test_url in browser.current_url
        
        # Add a small delay to ensure logging works
        time.sleep(1)
        
        print("✅ Test completed successfully - logging and pytest-order are working!")
        
    except Exception as e:
        print(f"❌ Exception in test_step1_navigate: {repr(e)}")
        raise

@pytest.mark.order(2)
def test_step2_verify_logging(browser):
    """Test to verify that logging directories are created properly."""
    import os
    from pathlib import Path
    
    # Check if log directories exist
    base_dir = Path(os.getcwd())
    log_dir = base_dir / "logs"
    test_logs_dir = log_dir / "test_logs"
    
    assert log_dir.exists(), f"Log directory {log_dir} should exist"
    assert test_logs_dir.exists(), f"Test logs directory {test_logs_dir} should exist"
    
    print("✅ Logging directories verified successfully!")

if __name__ == "__main__":
    # Run this test file directly
    pytest.main([__file__, "-v", "--tb=short"])
